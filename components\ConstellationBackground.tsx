import React, { useRef, useEffect, useMemo } from 'react';

interface ConstellationStar {
    x: number;
    y: number;
    size: number;
    brightness: number; // 0.3 à 1.0 pour différentes luminosités
    pulseSpeed: number;
    pulsePhase: number;
}

interface Constellation {
    name: string;
    stars: ConstellationStar[];
    connections: [number, number][]; // indices des étoiles à connecter
    position: { x: number; y: number }; // position relative de la constellation
    label: { x: number; y: number; text: string };
}

interface BackgroundStar {
    x: number;
    y: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationIdRef = useRef<number>();
    const lastFrameTimeRef = useRef<number>(0);
    const rotationAngleRef = useRef<number>(0);
    const labelVisibilityRef = useRef<Map<string, { visible: boolean; fadePhase: number; nextToggle: number }>>(new Map());
    const twilightPhaseRef = useRef<number>(0); // 0 = crépuscule, 1 = nuit profonde

    // Définition des 12 constellations du zodiaque
    const zodiacConstellations = useMemo((): Constellation[] => [
        // ARIES (Bélier) - Ligne 1, Position 1
        {
            name: 'ARIES',
            position: { x: 0.125, y: 0.15 },
            label: { x: 0.125, y: 0.08, text: 'A R I E S' },
            stars: [
                { x: 0.15, y: 0.25, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.12, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.08, y: 0.22, size: 1.5, brightness: 0.6, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.18, y: 0.15, size: 1.2, brightness: 0.5, pulseSpeed: 0.005, pulsePhase: 3 }
            ],
            connections: [[0, 1], [1, 2], [1, 3]]
        },
        // TAURUS (Taureau) - Ligne 1, Position 2
        {
            name: 'TAURUS',
            position: { x: 0.375, y: 0.15 },
            label: { x: 0.375, y: 0.08, text: 'T A U R U S' },
            stars: [
                { x: 0.35, y: 0.12, size: 2.8, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.38, y: 0.18, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.42, y: 0.15, size: 1.8, brightness: 0.7, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.32, y: 0.22, size: 1.5, brightness: 0.6, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.40, y: 0.25, size: 1.3, brightness: 0.5, pulseSpeed: 0.003, pulsePhase: 4 },
                { x: 0.36, y: 0.28, size: 1.2, brightness: 0.4, pulseSpeed: 0.004, pulsePhase: 5 }
            ],
            connections: [[0, 1], [1, 2], [0, 3], [3, 4], [4, 5], [1, 4]]
        },
        // GEMINI (Gémeaux) - Ligne 1, Position 3
        {
            name: 'GEMINI',
            position: { x: 0.625, y: 0.15 },
            label: { x: 0.625, y: 0.08, text: 'G E M I N I' },
            stars: [
                { x: 0.60, y: 0.12, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.65, y: 0.12, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 1 },
                { x: 0.58, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 2 },
                { x: 0.67, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 3 },
                { x: 0.62, y: 0.25, size: 1.5, brightness: 0.6, pulseSpeed: 0.002, pulsePhase: 4 },
                { x: 0.55, y: 0.22, size: 1.3, brightness: 0.5, pulseSpeed: 0.005, pulsePhase: 5 },
                { x: 0.70, y: 0.22, size: 1.3, brightness: 0.5, pulseSpeed: 0.005, pulsePhase: 6 }
            ],
            connections: [[0, 2], [2, 5], [5, 4], [4, 6], [6, 3], [3, 1], [0, 1]]
        }
    ], []);

    // Toutes les constellations du zodiaque (complètes)
    const allConstellations = useMemo((): Constellation[] => [
        ...zodiacConstellations,
        // CANCER (Cancer) - Ligne 1, Position 4
        {
            name: 'CANCER',
            position: { x: 0.875, y: 0.15 },
            label: { x: 0.875, y: 0.08, text: 'C A N C E R' },
            stars: [
                { x: 0.85, y: 0.12, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.88, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.90, y: 0.25, size: 2.2, brightness: 1.0, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.87, y: 0.28, size: 1.5, brightness: 0.6, pulseSpeed: 0.005, pulsePhase: 3 }
            ],
            connections: [[0, 1], [1, 2], [2, 3]]
        },
        // LEO (Lion) - Ligne 2, Position 1
        {
            name: 'LEO',
            position: { x: 0.125, y: 0.4 },
            label: { x: 0.125, y: 0.33, text: 'L E O' },
            stars: [
                { x: 0.08, y: 0.38, size: 2.8, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.12, y: 0.42, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.16, y: 0.45, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.14, y: 0.38, size: 1.5, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.18, y: 0.40, size: 1.3, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 },
                { x: 0.10, y: 0.48, size: 1.2, brightness: 0.5, pulseSpeed: 0.004, pulsePhase: 5 }
            ],
            connections: [[0, 1], [1, 3], [3, 4], [4, 2], [1, 5]]
        },
        // VIRGO (Vierge) - Ligne 2, Position 2
        {
            name: 'VIRGO',
            position: { x: 0.375, y: 0.4 },
            label: { x: 0.375, y: 0.33, text: 'V I R G O' },
            stars: [
                { x: 0.35, y: 0.38, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.38, y: 0.42, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.42, y: 0.45, size: 1.6, brightness: 0.7, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.40, y: 0.38, size: 1.4, brightness: 0.6, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.36, y: 0.48, size: 1.3, brightness: 0.5, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 3], [3, 2], [1, 4]]
        },
        // LIBRA (Balance) - Ligne 2, Position 3
        {
            name: 'LIBRA',
            position: { x: 0.625, y: 0.4 },
            label: { x: 0.625, y: 0.33, text: 'L I B R A' },
            stars: [
                { x: 0.60, y: 0.38, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.65, y: 0.42, size: 2.2, brightness: 1.0, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.62, y: 0.48, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.58, y: 0.45, size: 1.5, brightness: 0.6, pulseSpeed: 0.005, pulsePhase: 3 }
            ],
            connections: [[0, 1], [1, 2], [2, 3], [3, 0]]
        },
        // SCORPIO (Scorpion) - Ligne 2, Position 4
        {
            name: 'SCORPIO',
            position: { x: 0.875, y: 0.4 },
            label: { x: 0.875, y: 0.33, text: 'S C O R P I O' },
            stars: [
                { x: 0.85, y: 0.38, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.88, y: 0.42, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.90, y: 0.48, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.92, y: 0.45, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.87, y: 0.35, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 2], [2, 3], [0, 4]]
        },
        // SAGITTARIUS (Sagittaire) - Ligne 3, Position 1
        {
            name: 'SAGITTARIUS',
            position: { x: 0.125, y: 0.65 },
            label: { x: 0.125, y: 0.58, text: 'S A G I T T A R I U S' },
            stars: [
                { x: 0.08, y: 0.62, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.12, y: 0.65, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.15, y: 0.68, size: 2.2, brightness: 1.0, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.18, y: 0.65, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.10, y: 0.70, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 2], [2, 3], [1, 4]]
        },
        // CAPRICORN (Capricorne) - Ligne 3, Position 2
        {
            name: 'CAPRICORN',
            position: { x: 0.375, y: 0.65 },
            label: { x: 0.375, y: 0.58, text: 'C A P R I C O R N' },
            stars: [
                { x: 0.35, y: 0.62, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.38, y: 0.68, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.42, y: 0.65, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.40, y: 0.72, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.36, y: 0.75, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 2], [1, 3], [3, 4]]
        },
        // AQUARIUS (Verseau) - Ligne 3, Position 3
        {
            name: 'AQUARIUS',
            position: { x: 0.625, y: 0.65 },
            label: { x: 0.625, y: 0.58, text: 'A Q U A R I U S' },
            stars: [
                { x: 0.60, y: 0.62, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.63, y: 0.68, size: 2.2, brightness: 1.0, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.67, y: 0.65, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.65, y: 0.72, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.58, y: 0.70, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 2], [1, 3], [0, 4]]
        },
        // PISCES (Poissons) - Ligne 3, Position 4
        {
            name: 'PISCES',
            position: { x: 0.875, y: 0.65 },
            label: { x: 0.875, y: 0.58, text: 'P I S C E S' },
            stars: [
                { x: 0.85, y: 0.62, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.88, y: 0.68, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.90, y: 0.65, size: 2.2, brightness: 1.0, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.92, y: 0.72, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.87, y: 0.75, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 },
                { x: 0.83, y: 0.70, size: 1.3, brightness: 0.5, pulseSpeed: 0.004, pulsePhase: 5 }
            ],
            connections: [[0, 1], [1, 2], [2, 3], [3, 4], [4, 5], [5, 0]]
        }
    ], [zodiacConstellations]);

    // Couleurs des étoiles
    const starColors = useMemo(() => [
        '#FFFFFF', '#F0F8FF', '#87CEEB', '#B0E0E6', '#E0FFFF'
    ], []);

    // Couleurs du crépuscule (très subtiles comme dans votre image)
    const twilightColors = useMemo(() => ({
        // Phase crépuscule (début)
        twilight: {
            top: '#1a237e',      // Bleu nuit profond en haut
            mid: '#3949ab',      // Bleu moyen
            bottom: '#ff7043'    // Orange très subtil en bas
        },
        // Phase intermédiaire
        evening: {
            top: '#0d1421',      // Bleu très sombre
            mid: '#1e2a4a',      // Bleu-gris
            bottom: '#4a2c2a'    // Orange-brun très subtil
        },
        // Phase nuit profonde (fin)
        night: {
            top: '#000008',      // Noir profond
            mid: '#000012',      // Noir légèrement bleuté
            bottom: '#000008'    // Noir profond
        }
    }), []);

    // Étoiles de fond microscopiques - BEAUCOUP plus nombreuses !
    const backgroundStars = useMemo(() => {
        const stars: BackgroundStar[] = [];

        // 3 couches d'étoiles pour un effet de profondeur
        // Couche 1: Étoiles très lointaines (microscopiques)
        for (let i = 0; i < 800; i++) {
            stars.push({
                x: Math.random(),
                y: Math.random(),
                size: Math.random() * 0.3 + 0.1, // Très petites
                opacity: Math.random() * 0.15 + 0.05, // Très faibles
                pulseSpeed: Math.random() * 0.001 + 0.0005,
                pulsePhase: Math.random() * Math.PI * 2
            });
        }

        // Couche 2: Étoiles moyennes
        for (let i = 0; i < 400; i++) {
            stars.push({
                x: Math.random(),
                y: Math.random(),
                size: Math.random() * 0.6 + 0.2,
                opacity: Math.random() * 0.25 + 0.1,
                pulseSpeed: Math.random() * 0.002 + 0.001,
                pulsePhase: Math.random() * Math.PI * 2
            });
        }

        // Couche 3: Étoiles plus visibles
        for (let i = 0; i < 200; i++) {
            stars.push({
                x: Math.random(),
                y: Math.random(),
                size: Math.random() * 1.0 + 0.3,
                opacity: Math.random() * 0.4 + 0.2,
                pulseSpeed: Math.random() * 0.003 + 0.001,
                pulsePhase: Math.random() * Math.PI * 2
            });
        }

        return stars;
    }, []);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d', {
            alpha: false,
            desynchronized: true
        });
        if (!ctx) return;

        let mouseX = -1;
        let mouseY = -1;

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        // Fonction pour interpoler entre deux couleurs hexadécimales
        const interpolateColor = (color1: string, color2: string, factor: number): string => {
            const hex1 = color1.replace('#', '');
            const hex2 = color2.replace('#', '');

            const r1 = parseInt(hex1.substring(0, 2), 16);
            const g1 = parseInt(hex1.substring(2, 4), 16);
            const b1 = parseInt(hex1.substring(4, 6), 16);

            const r2 = parseInt(hex2.substring(0, 2), 16);
            const g2 = parseInt(hex2.substring(2, 4), 16);
            const b2 = parseInt(hex2.substring(4, 6), 16);

            const r = Math.round(r1 + (r2 - r1) * factor);
            const g = Math.round(g1 + (g2 - g1) * factor);
            const b = Math.round(b1 + (b2 - b1) * factor);

            return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
        };

        // Fonction pour créer le gradient de crépuscule dynamique
        const createTwilightGradient = (phase: number): CanvasGradient => {
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);

            // Interpolation très douce entre les phases
            let topColor, midColor, bottomColor;

            if (phase < 0.5) {
                // Transition crépuscule -> soirée
                const factor = phase * 2; // 0 à 1
                topColor = interpolateColor(twilightColors.twilight.top, twilightColors.evening.top, factor);
                midColor = interpolateColor(twilightColors.twilight.mid, twilightColors.evening.mid, factor);
                bottomColor = interpolateColor(twilightColors.twilight.bottom, twilightColors.evening.bottom, factor);
            } else {
                // Transition soirée -> nuit profonde
                const factor = (phase - 0.5) * 2; // 0 à 1
                topColor = interpolateColor(twilightColors.evening.top, twilightColors.night.top, factor);
                midColor = interpolateColor(twilightColors.evening.mid, twilightColors.night.mid, factor);
                bottomColor = interpolateColor(twilightColors.evening.bottom, twilightColors.night.bottom, factor);
            }

            // Création du gradient avec transitions très douces
            gradient.addColorStop(0, topColor);
            gradient.addColorStop(0.3, midColor);
            gradient.addColorStop(0.7, midColor);
            gradient.addColorStop(1, bottomColor);

            return gradient;
        };

        // Fonction pour dessiner une étoile de constellation avec effets SPECTACULAIRES
        const drawConstellationStar = (star: ConstellationStar, canvasX: number, canvasY: number, time: number, twilightPhase: number) => {
            const twinkle = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.3 + 0.7;
            const sparkle = Math.sin(time * star.pulseSpeed * 2.5 + star.pulsePhase) * 0.2 + 0.8;

            // Adaptation de l'opacité selon la phase du crépuscule
            // Les étoiles deviennent plus visibles quand la nuit tombe
            const twilightOpacityBoost = 0.3 + (twilightPhase * 0.7); // 0.3 à 1.0
            const currentOpacity = star.brightness * twinkle * twilightOpacityBoost;

            // Couleur des étoiles avec une légère teinte chaude pendant le crépuscule
            const baseColor = starColors[0];
            let color = baseColor;

            // Pendant le crépuscule (phase < 0.3), ajouter une très légère teinte dorée
            if (twilightPhase < 0.3) {
                const warmFactor = (0.3 - twilightPhase) / 0.3; // 1 à 0
                const warmTint = Math.round(warmFactor * 15); // Très subtil
                color = `rgb(${255}, ${255 - warmTint}, ${255 - warmTint * 2})`;
            }

            ctx.save();

            // EFFET 1: Halo externe massif pour les étoiles très brillantes
            if (star.brightness > 0.8) {
                ctx.globalAlpha = currentOpacity * 0.1;
                const outerGradient = ctx.createRadialGradient(
                    canvasX, canvasY, 0,
                    canvasX, canvasY, star.size * 8
                );
                outerGradient.addColorStop(0, color);
                outerGradient.addColorStop(0.2, color + '40');
                outerGradient.addColorStop(0.5, color + '20');
                outerGradient.addColorStop(1, color + '00');

                ctx.fillStyle = outerGradient;
                ctx.beginPath();
                ctx.arc(canvasX, canvasY, star.size * 8, 0, Math.PI * 2);
                ctx.fill();
            }

            // EFFET 2: Halo moyen pour toutes les étoiles brillantes
            if (star.brightness > 0.6) {
                ctx.globalAlpha = currentOpacity * 0.3;
                const midGradient = ctx.createRadialGradient(
                    canvasX, canvasY, 0,
                    canvasX, canvasY, star.size * 4
                );
                midGradient.addColorStop(0, color);
                midGradient.addColorStop(0.4, color + '80');
                midGradient.addColorStop(0.8, color + '40');
                midGradient.addColorStop(1, color + '00');

                ctx.fillStyle = midGradient;
                ctx.beginPath();
                ctx.arc(canvasX, canvasY, star.size * 4, 0, Math.PI * 2);
                ctx.fill();
            }

            // EFFET 3: Rayons de lumière pour les étoiles principales
            if (star.brightness > 0.9) {
                ctx.globalAlpha = currentOpacity * sparkle * 0.6;
                ctx.strokeStyle = color;
                ctx.lineWidth = 0.5;

                // 4 rayons principaux
                for (let i = 0; i < 4; i++) {
                    const angle = (i * Math.PI / 2) + (time * 0.001);
                    const rayLength = star.size * 6;

                    ctx.beginPath();
                    ctx.moveTo(
                        canvasX + Math.cos(angle) * star.size,
                        canvasY + Math.sin(angle) * star.size
                    );
                    ctx.lineTo(
                        canvasX + Math.cos(angle) * rayLength,
                        canvasY + Math.sin(angle) * rayLength
                    );
                    ctx.stroke();
                }
            }

            // EFFET 4: Corps de l'étoile avec gradient interne
            ctx.globalAlpha = currentOpacity;
            const coreGradient = ctx.createRadialGradient(
                canvasX, canvasY, 0,
                canvasX, canvasY, star.size
            );
            coreGradient.addColorStop(0, '#FFFFFF');
            coreGradient.addColorStop(0.7, color);
            coreGradient.addColorStop(1, color + 'CC');

            ctx.fillStyle = coreGradient;
            ctx.beginPath();
            ctx.arc(canvasX, canvasY, star.size, 0, Math.PI * 2);
            ctx.fill();

            // EFFET 5: Point central ultra-brillant
            if (star.brightness > 0.7) {
                ctx.globalAlpha = currentOpacity * sparkle;
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.arc(canvasX, canvasY, star.size * 0.3, 0, Math.PI * 2);
                ctx.fill();
            }

            ctx.restore();
        };

        // Fonction pour dessiner les connexions d'une constellation
        const drawConstellationConnections = (constellation: Constellation, canvasWidth: number, canvasHeight: number, twilightPhase: number) => {
            ctx.save();

            // Adaptation de l'opacité des connexions selon la phase du crépuscule
            const connectionOpacity = 0.2 + (twilightPhase * 0.3); // 0.2 à 0.5
            ctx.globalAlpha = connectionOpacity;

            // Couleur des connexions avec légère teinte chaude pendant le crépuscule
            let strokeColor = starColors[0];
            if (twilightPhase < 0.3) {
                const warmFactor = (0.3 - twilightPhase) / 0.3;
                const warmTint = Math.round(warmFactor * 10);
                strokeColor = `rgb(${255}, ${255 - warmTint}, ${255 - warmTint * 2})`;
            }

            ctx.strokeStyle = strokeColor;
            ctx.lineWidth = 1;

            constellation.connections.forEach(([startIdx, endIdx]) => {
                const startStar = constellation.stars[startIdx];
                const endStar = constellation.stars[endIdx];

                if (startStar && endStar) {
                    const startX = startStar.x * canvasWidth;
                    const startY = startStar.y * canvasHeight;
                    const endX = endStar.x * canvasWidth;
                    const endY = endStar.y * canvasHeight;

                    ctx.beginPath();
                    ctx.moveTo(startX, startY);
                    ctx.lineTo(endX, endY);
                    ctx.stroke();
                }
            });

            ctx.restore();
        };

        // Fonction pour dessiner le label d'une constellation avec animation
        const drawConstellationLabel = (constellation: Constellation, canvasWidth: number, canvasHeight: number, time: number) => {
            const labelX = constellation.label.x * canvasWidth;
            const labelY = constellation.label.y * canvasHeight;

            // Gestion de la visibilité des labels
            let labelState = labelVisibilityRef.current.get(constellation.name);
            if (!labelState) {
                labelState = {
                    visible: false,
                    fadePhase: 0,
                    nextToggle: time + Math.random() * 5000 + 3000 // Entre 3 et 8 secondes
                };
                labelVisibilityRef.current.set(constellation.name, labelState);
            }

            // Vérifier s'il faut changer l'état
            if (time > labelState.nextToggle) {
                labelState.visible = !labelState.visible;
                labelState.fadePhase = 0;
                labelState.nextToggle = time + (labelState.visible ?
                    Math.random() * 3000 + 4000 : // Visible pendant 4-7 secondes
                    Math.random() * 4000 + 2000   // Invisible pendant 2-6 secondes
                );
            }

            // Animation de fade
            if (labelState.visible) {
                labelState.fadePhase = Math.min(1, labelState.fadePhase + 0.02);
            } else {
                labelState.fadePhase = Math.max(0, labelState.fadePhase - 0.02);
            }

            // Dessiner le label si visible
            if (labelState.fadePhase > 0) {
                const opacity = labelState.fadePhase * 0.8;

                ctx.save();

                // Effet de lueur derrière le texte
                ctx.globalAlpha = opacity * 0.3;
                ctx.fillStyle = starColors[0];
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.shadowColor = starColors[0];
                ctx.shadowBlur = 10;
                ctx.fillText(constellation.label.text, labelX, labelY);

                // Texte principal
                ctx.globalAlpha = opacity;
                ctx.shadowBlur = 5;
                ctx.fillText(constellation.label.text, labelX, labelY);

                ctx.restore();
            }
        };

        // Fonction pour dessiner les étoiles de fond MICROSCOPIQUES mais LUMINEUSES
        const drawBackgroundStars = (time: number, twilightPhase: number) => {
            backgroundStars.forEach(star => {
                const twinkle = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.4 + 0.6;
                const sparkle = Math.sin(time * star.pulseSpeed * 3 + star.pulsePhase) * 0.3 + 0.7;

                // Les étoiles de fond deviennent plus visibles avec la nuit
                const twilightVisibility = 0.2 + (twilightPhase * 0.8); // 0.2 à 1.0
                const currentOpacity = star.opacity * twinkle * sparkle * twilightVisibility;

                const x = star.x * canvas.width;
                const y = star.y * canvas.height;

                ctx.save();

                // Halo très subtil pour les étoiles de fond
                if (star.size > 0.4) {
                    ctx.globalAlpha = currentOpacity * 0.2;
                    const gradient = ctx.createRadialGradient(x, y, 0, x, y, star.size * 2);
                    gradient.addColorStop(0, starColors[0]);
                    gradient.addColorStop(0.5, starColors[0] + '40');
                    gradient.addColorStop(1, starColors[0] + '00');

                    ctx.fillStyle = gradient;
                    ctx.beginPath();
                    ctx.arc(x, y, star.size * 2, 0, Math.PI * 2);
                    ctx.fill();
                }

                // Corps de l'étoile avec plus de luminosité
                ctx.globalAlpha = Math.min(1, currentOpacity * 1.5); // Augmentation de la luminosité
                ctx.fillStyle = starColors[0];
                ctx.beginPath();
                ctx.arc(x, y, star.size, 0, Math.PI * 2);
                ctx.fill();

                // Point central ultra-lumineux pour les plus grandes étoiles de fond
                if (star.size > 0.6) {
                    ctx.globalAlpha = Math.min(1, currentOpacity * 2);
                    ctx.fillStyle = '#FFFFFF';
                    ctx.beginPath();
                    ctx.arc(x, y, star.size * 0.3, 0, Math.PI * 2);
                    ctx.fill();
                }

                ctx.restore();
            });
        };

        // Fonction pour appliquer la rotation planisphère
        const applyPlanisphereRotation = (x: number, y: number, centerX: number, centerY: number, angle: number) => {
            const dx = x - centerX;
            const dy = y - centerY;
            const cos = Math.cos(angle);
            const sin = Math.sin(angle);

            return {
                x: dx * cos - dy * sin + centerX,
                y: dx * sin + dy * cos + centerY
            };
        };

        // Animation des constellations du zodiaque avec rotation planisphère
        const animate = (time: number) => {
            const deltaTime = time - lastFrameTimeRef.current;
            const targetFrameTime = 1000 / 60; // 60 FPS

            if (deltaTime < targetFrameTime) {
                animationIdRef.current = requestAnimationFrame(animate);
                return;
            }

            lastFrameTimeRef.current = time;

            // Mise à jour de l'angle de rotation (très lent comme une vraie planisphère)
            rotationAngleRef.current += 0.0002; // Rotation très lente

            // Mise à jour de la phase crépuscule (cycle très lent et subtil)
            // Cycle complet en 120 secondes (2 minutes) pour une transition très douce
            const twilightCycleSpeed = 0.000008; // Très lent pour subtilité maximale
            twilightPhaseRef.current = (Math.sin(time * twilightCycleSpeed) + 1) / 2; // 0 à 1

            // Fond avec gradient de crépuscule dynamique
            const twilightGradient = createTwilightGradient(twilightPhaseRef.current);
            ctx.fillStyle = twilightGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Dessiner les étoiles de fond avec adaptation au crépuscule
            drawBackgroundStars(time, twilightPhaseRef.current);

            // Centre de rotation (centre de l'écran)
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // Dessiner chaque constellation du zodiaque avec rotation
            allConstellations.forEach(constellation => {
                // Créer une constellation rotée
                const rotatedConstellation = {
                    ...constellation,
                    stars: constellation.stars.map(star => {
                        const absoluteX = star.x * canvas.width;
                        const absoluteY = star.y * canvas.height;
                        const rotated = applyPlanisphereRotation(absoluteX, absoluteY, centerX, centerY, rotationAngleRef.current);
                        return {
                            ...star,
                            x: rotated.x / canvas.width,
                            y: rotated.y / canvas.height
                        };
                    })
                };

                // Dessiner les connexions en premier
                drawConstellationConnections(rotatedConstellation, canvas.width, canvas.height, twilightPhaseRef.current);

                // Dessiner les étoiles avec leurs effets spectaculaires
                rotatedConstellation.stars.forEach(star => {
                    const canvasX = star.x * canvas.width;
                    const canvasY = star.y * canvas.height;
                    drawConstellationStar(star, canvasX, canvasY, time, twilightPhaseRef.current);
                });

                // Dessiner le label avec animation
                const labelX = constellation.label.x * canvas.width;
                const labelY = constellation.label.y * canvas.height;
                const rotatedLabel = applyPlanisphereRotation(labelX, labelY, centerX, centerY, rotationAngleRef.current);
                const rotatedLabelConstellation = {
                    ...constellation,
                    label: {
                        ...constellation.label,
                        x: rotatedLabel.x / canvas.width,
                        y: rotatedLabel.y / canvas.height
                    }
                };
                drawConstellationLabel(rotatedLabelConstellation, canvas.width, canvas.height, time);
            });

            // Effet de connexion avec la souris (optionnel)
            if (mouseX >= 0 && mouseY >= 0) {
                // Trouver les étoiles proches de la souris
                allConstellations.forEach(constellation => {
                    constellation.stars.forEach(star => {
                        const canvasX = star.x * canvas.width;
                        const canvasY = star.y * canvas.height;
                        const dx = mouseX - canvasX;
                        const dy = mouseY - canvasY;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 100) {
                            const opacity = Math.max(0, (100 - distance) / 100) * 0.3;
                            ctx.save();
                            ctx.globalAlpha = opacity;
                            ctx.strokeStyle = starColors[0];
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(canvasX, canvasY);
                            ctx.lineTo(mouseX, mouseY);
                            ctx.stroke();
                            ctx.restore();
                        }
                    });
                });

                // Point souris
                ctx.save();
                ctx.globalAlpha = 0.5;
                ctx.fillStyle = starColors[0];
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }

            animationIdRef.current = requestAnimationFrame(animate);
        };

        // Gestionnaires d'événements
        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        const handleMouseLeave = () => {
            mouseX = -1;
            mouseY = -1;
        };

        const handleMouseEnter = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        // Initialisation
        initialize();
        animate(0);

        // Event listeners
        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        return () => {
            if (animationIdRef.current) {
                cancelAnimationFrame(animationIdRef.current);
            }
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
        };
    }, [allConstellations, backgroundStars, starColors]);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
