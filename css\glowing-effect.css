/* Effet néon laser pour les cartes - Extrait de GlowingCard.tsx */

@property --angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

:root {
  --gemini-blue-light: #2190F6;
  --gemini-blue-purple: #6689EF;
  --gemini-purple: rgb(77, 70, 175);
  --gemini-salmon: rgb(235, 73, 114);
}

/* Classe utilitaire pour l'effet néon laser */
.glowing-effect {
  position: relative;
  /* Créer un nouveau contexte de stacking pour isoler l'effet */
  isolation: isolate;
}

.glowing-effect::before,
.glowing-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* Z-index très négatif pour rester derrière tout le contenu */
  z-index: -10;
  width: 100%;
  height: 100%;
  border-radius: inherit; /* Hérite du border-radius du parent */
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
  /* Empêcher l'interaction avec l'effet */
  pointer-events: none;
}

.glowing-effect::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
  z-index: -11;
}

.glowing-effect::after {
  filter: brightness(2.5);
  z-index: -10;
}

.glowing-effect:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}

.glowing-effect:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}

@keyframes spin {
  from { --angle: 0deg; }
  to { --angle: 360deg; }
}

/* Assurer que le contenu reste au-dessus de l'effet */
.glowing-effect > * {
  position: relative;
  z-index: 1;
}

/* Correction spécifique pour les cartes avec backdrop-blur */
.glowing-effect.backdrop-blur-sm,
.glowing-effect[class*="backdrop-blur"] {
  /* Assurer que le fond de la carte reste opaque pour masquer l'effet */
  background-color: rgba(27, 28, 29, 0.9) !important;
}

/* Alternative : masquer l'effet derrière les cartes semi-transparentes */
.glowing-effect[class*="bg-brand-surface"] {
  /* Renforcer l'opacité du fond pour les cartes About */
  background-color: rgba(40, 42, 44, 0.95) !important;
}
